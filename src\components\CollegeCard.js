'use client';

import Link from 'next/link';
import { MapPin, Users, Award, TrendingUp, Train, Calendar, ExternalLink } from 'lucide-react';
import { formatCurrency, formatNIRF, getWhatsAppLink } from '../lib/collegeData';
import OptimizedImage from './ui/OptimizedImage';

export default function CollegeCard({ college, showCompareButton = true, isCompact = false, priority = false }) {
  if (isCompact) {
    return (
      <div className="card p-4 card-hover">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="bg-primary-100 text-primary-600 px-2 py-1 rounded-full text-xs font-semibold">
              #{college.ranking}
            </div>
            <h3 className="font-semibold text-gray-900 text-sm">{college.acronym}</h3>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-secondary-600">{college.placementRate}%</div>
            <div className="text-xs text-gray-500">Placement</div>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            {formatCurrency(college.highestPackage)} highest
          </div>
          <Link
            href={`/colleges/${college.id}`}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View Details
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="card p-0 card-hover overflow-hidden">
      {/* College Image */}
      <div className="relative h-48 w-full">
        <OptimizedImage
          src={college.image}
          alt={`${college.name} (${college.acronym}) - Campus view`}
          width={400}
          height={192}
          className="w-full h-full"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority={priority}
        />
        <div className="absolute top-4 left-4">
          <div className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
            Rank #{college.ranking}
          </div>
        </div>
        {college.metroAccess && (
          <div className="absolute top-4 right-4">
            <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
              <Train className="h-3 w-3" />
              <span>Metro</span>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="text-right ml-auto">
            <div className="text-sm text-gray-500">{formatNIRF(college.nirf)}</div>
            <div className="text-xs text-gray-400 flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              Est. {college.establishedYear}
            </div>
          </div>
        </div>

        {/* College Name */}
        <div className="mb-4">
          <h3 className="text-xl font-bold text-gray-900 mb-1">{college.name}</h3>
          <p className="text-lg font-semibold text-primary-600">{college.acronym}</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{college.placementRate}%</div>
            <div className="text-sm text-gray-600">Placement Rate</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(college.highestPackage)}
            </div>
            <div className="text-sm text-gray-600">Highest Package</div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="space-y-2 mb-6">
          <div className="flex items-center text-sm text-gray-600">
            <MapPin className="h-4 w-4 mr-2 text-gray-400" />
            <span className="truncate">{college.campusSize}</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Users className="h-4 w-4 mr-2 text-gray-400" />
            <span>Multiple Engineering Programs</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <TrendingUp className="h-4 w-4 mr-2 text-gray-400" />
            <span>Strong Industry Connections</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Link
            href={`/colleges/${college.id}`}
            className="flex-1 bg-primary-600 hover:bg-primary-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 text-center flex items-center justify-center space-x-2"
          >
            <span>View Details</span>
            <ExternalLink className="h-4 w-4" />
          </Link>
          
          {showCompareButton && (
            <button
              onClick={() => {
                // Add to comparison logic would go here
                alert(`Added ${college.acronym} to comparison`);
              }}
              className="flex-1 bg-white hover:bg-gray-50 text-primary-600 font-semibold py-2 px-4 rounded-lg border-2 border-primary-600 transition-all duration-200 text-center"
            >
              Compare
            </button>
          )}
        </div>

        {/* WhatsApp Consultation */}
        <div className="mt-4 pt-4 border-t border-gray-100">
          <a
            href={getWhatsAppLink(college.name)}
            target="_blank"
            rel="noopener noreferrer"
            className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 text-center text-sm flex items-center justify-center space-x-2"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
            </svg>
            <span>Get Guidance</span>
          </a>
        </div>
      </div>
    </div>
  );
}
