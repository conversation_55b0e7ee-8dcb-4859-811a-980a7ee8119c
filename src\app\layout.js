import '../styles/globals.css'
import Header from '../components/Header'
import Footer from '../components/Footer'
import ChatbotProvider from '../components/ChatbotProvider'
import ServiceWorkerRegistration from '../components/ServiceWorkerRegistration'
import PerformanceMonitor from '../components/PerformanceMonitor'

export const metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  title: 'Top Engineering Colleges in Bangalore | College Comparison & Rankings',
  description: 'Compare top engineering colleges in Bangalore. Get detailed information about placements, courses, rankings, and campus facilities. Find your perfect engineering college match.',
  keywords: 'engineering colleges bangalore, college comparison, placement statistics, RVCE, MSRIT, PES University, BMS College, college rankings',
  authors: [{ name: 'College Comparison Platform' }],
  openGraph: {
    title: 'Top Engineering Colleges in Bangalore | College Comparison',
    description: 'Compare top engineering colleges in Bangalore with detailed placement statistics, course information, and campus facilities.',
    type: 'website',
    locale: 'en_IN',
    siteName: 'Bangalore Engineering Colleges',
  },
  twitter: {
    card: 'summary',
    title: 'Top Engineering Colleges in Bangalore',
    description: 'Compare engineering colleges in Bangalore with detailed insights.',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#2563eb" />
        
        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "Bangalore Engineering Colleges Comparison",
              "description": "Compare top engineering colleges in Bangalore with detailed placement statistics and course information",
              "url": "https://bangalore-engineering-colleges.com",
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://bangalore-engineering-colleges.com/colleges?search={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
      </head>
      <body className="min-h-screen bg-gray-50 flex flex-col">
        <Header />
        <main className="flex-grow">
          {children}
        </main>
        <Footer />

        {/* Performance Monitoring */}
        <PerformanceMonitor />

        {/* Service Worker Registration */}
        <ServiceWorkerRegistration />

        {/* Chatbot Integration */}
        <ChatbotProvider />

        {/* WhatsApp Float Button */}
        <div className="fixed bottom-6 right-6 z-50">
          <a
            href="https://wa.me/************?text=Hi! I need guidance on engineering colleges in Bangalore."
            target="_blank"
            rel="noopener noreferrer"
            className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 animate-bounce-slow"
            aria-label="WhatsApp Consultation"
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
            </svg>
          </a>
        </div>
      </body>
    </html>
  )
}
