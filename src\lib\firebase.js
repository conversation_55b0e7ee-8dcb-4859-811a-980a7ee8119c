// Firebase configuration and initialization
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';

// Conditional imports for client-side only
let auth, googleProvider, facebookProvider, analytics;

if (typeof window !== 'undefined') {
  import('firebase/auth').then((authModule) => {
    auth = authModule.getAuth();
    googleProvider = new authModule.GoogleAuthProvider();
    facebookProvider = new authModule.FacebookAuthProvider();

    // Configure providers
    googleProvider.setCustomParameters({
      prompt: 'select_account'
    });

    facebookProvider.setCustomParameters({
      display: 'popup'
    });
  });

  import('firebase/analytics').then((analyticsModule) => {
    analytics = analyticsModule.getAnalytics();
  });
}

// Firebase configuration
const firebaseConfig = {
  // Replace with your Firebase config
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "demo-key",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "demo-project.firebaseapp.com",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "demo-project",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "demo-project.appspot.com",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "*********",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "1:*********:web:abcdef",
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || "G-ABCDEF"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
export const db = getFirestore(app);

// Export auth and providers (will be initialized client-side)
export { auth, googleProvider, facebookProvider, analytics };

export default app;
