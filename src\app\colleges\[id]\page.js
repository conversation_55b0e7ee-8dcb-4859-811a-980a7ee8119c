'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import OptimizedImage from '../../../components/ui/OptimizedImage';
import { 
  ArrowLeft, 
  MapPin, 
  Calendar, 
  Users, 
  Award, 
  TrendingUp, 
  Train,
  ExternalLink,
  Phone,
  Mail,
  Globe,
  Building,
  GraduationCap,
  Target
} from 'lucide-react';
import { getCollegeById, formatCurrency, formatNIRF, getWhatsAppLink } from '../../../lib/collegeData';

export default function CollegeDetailPage() {
  const params = useParams();
  const [college, setCollege] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params?.id) {
      const loadCollege = async () => {
        try {
          const collegeData = await getCollegeById(params.id);
          setCollege(collegeData);
          setLoading(false);
        } catch (error) {
          console.error('Failed to load college:', error);
          setLoading(false);
        }
      };
      loadCollege();
    }
  }, [params?.id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    );
  }

  if (!college) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">College Not Found</h1>
          <Link href="/colleges" className="btn-primary">
            Back to Colleges
          </Link>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Quick Facts', icon: Target },
    { id: 'courses', label: 'Courses & Fees', icon: GraduationCap },
    { id: 'placements', label: 'Placement Statistics', icon: TrendingUp },
    { id: 'campus', label: 'Campus Information', icon: Building },
    { id: 'transport', label: 'Transportation', icon: Train },
  ];

  const quickFacts = [
    { label: 'Ranking', value: `#${college.ranking}`, icon: Award },
    { label: 'NIRF Ranking', value: formatNIRF(college.nirf), icon: Award },
    { label: 'Established', value: college.establishedYear, icon: Calendar },
    { label: 'Campus Size', value: college.campusSize, icon: MapPin },
    { label: 'Placement Rate', value: `${college.placementRate}%`, icon: TrendingUp },
    { label: 'Highest Package', value: formatCurrency(college.highestPackage), icon: Users },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="container-max py-6">
          <div className="flex items-center space-x-4 mb-6">
            <Link
              href="/colleges"
              className="flex items-center space-x-2 text-gray-600 hover:text-primary-600 transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Colleges</span>
            </Link>
          </div>

          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8">
            {/* College Info */}
            <div className="flex-1">
              <div className="flex items-start space-x-4 mb-6">
                <div className="bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-semibold">
                  Rank #{college.ranking}
                </div>
                {college.metroAccess && (
                  <div className="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <Train className="h-4 w-4" />
                    <span>Metro Accessible</span>
                  </div>
                )}
              </div>

              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                {college.name}
              </h1>
              <p className="text-xl font-semibold text-primary-600 mb-4">
                {college.acronym}
              </p>

              <div className="flex items-start space-x-2 text-gray-600 mb-6">
                <MapPin className="h-5 w-5 mt-0.5 flex-shrink-0" />
                <p className="text-sm leading-relaxed">{college.address}</p>
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">{college.placementRate}%</div>
                  <div className="text-sm text-gray-600">Placement Rate</div>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(college.highestPackage)}
                  </div>
                  <div className="text-sm text-gray-600">Highest Package</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-600">{college.campusSize}</div>
                  <div className="text-sm text-gray-600">Campus Size</div>
                </div>
              </div>
            </div>

            {/* CTA Section */}
            <div className="lg:w-80">
              {/* College Image */}
              <div className="card p-0 mb-6 overflow-hidden">
                <div className="relative h-48 w-full">
                  <OptimizedImage
                    src={college.image}
                    alt={`${college.name} (${college.acronym}) - Campus view`}
                    width={320}
                    height={192}
                    className="w-full h-full"
                    sizes="(max-width: 768px) 100vw, 320px"
                    priority={true}
                  />
                </div>
              </div>

              <div className="card p-6 sticky top-24">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Get Personalized Guidance
                </h3>
                <p className="text-gray-600 mb-6 text-sm">
                  Get expert advice about {college.acronym} admission process, course selection, and career prospects.
                </p>
                <div className="space-y-3">
                  <a
                    href={getWhatsAppLink(college.name)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 text-center flex items-center justify-center space-x-2"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                    <span>WhatsApp Consultation</span>
                  </a>
                  <button
                    onClick={() => alert(`Added ${college.acronym} to comparison`)}
                    className="w-full btn-secondary"
                  >
                    Add to Compare
                  </button>
                  {college.locationUrl && (
                    <a
                      href={college.locationUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-all duration-200 text-center flex items-center justify-center space-x-2 text-sm"
                    >
                      <MapPin className="w-4 h-4" />
                      <span>View on Maps</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-30">
        <div className="container-max">
          <nav className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="container-max py-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {quickFacts.map((fact, index) => {
                const Icon = fact.icon;
                return (
                  <div key={index} className="card p-6 text-center">
                    <Icon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
                    <div className="text-2xl font-bold text-gray-900 mb-1">{fact.value}</div>
                    <div className="text-sm text-gray-600">{fact.label}</div>
                  </div>
                );
              })}
            </div>

            <div className="card p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">About {college.acronym}</h2>
              <div className="prose max-w-none text-gray-700 leading-relaxed">
                {college.summary.split('\n').map((paragraph, index) => (
                  <p key={index} className="mb-4">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'courses' && (
          <div className="card p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Courses Offered</h2>
            <div className="prose max-w-none">
              <div 
                className="text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{ 
                  __html: college.coursesOffered
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/- /g, '<br/>• ')
                }}
              />
            </div>
          </div>
        )}

        {activeTab === 'placements' && (
          <div className="space-y-8">
            <div className="card p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Placement Details</h2>
              <div className="prose max-w-none text-gray-700 leading-relaxed">
                {college.placementDetails}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'campus' && (
          <div className="space-y-8">
            <div className="card p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Infrastructure</h2>
              <p className="text-gray-700 leading-relaxed mb-6">{college.infrastructure}</p>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Laboratory Facilities</h3>
              <p className="text-gray-700 leading-relaxed">{college.labs}</p>
            </div>
          </div>
        )}

        {activeTab === 'transport' && (
          <div className="card p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Transportation & Connectivity</h2>
            <p className="text-gray-700 leading-relaxed">{college.busAndMetroConvenience}</p>
            
            {college.coordinates && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Location Coordinates</h3>
                <p className="text-gray-600">{college.coordinates}</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
