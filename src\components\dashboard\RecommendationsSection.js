'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  <PERSON>rk<PERSON>, 
  TrendingUp, 
  Heart, 
  Settings, 
  ArrowRight,
  RefreshCw,
  Star,
  MapPin,
  Award
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { getRecommendations } from '../../lib/recommendationEngine';
import CollegeCard from '../CollegeCard';
import FavoriteButton from '../FavoriteButton';

const RecommendationsSection = ({ limit = 6, showHeader = true }) => {
  const { user } = useAuth();
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [recommendationType, setRecommendationType] = useState('hybrid');
  const [refreshing, setRefreshing] = useState(false);

  const recommendationTypes = [
    { value: 'hybrid', label: 'For You', icon: Sparkles, description: 'Personalized recommendations' },
    { value: 'trending', label: 'Trending', icon: TrendingUp, description: 'Popular choices' },
    { value: 'content', label: 'Similar', icon: Heart, description: 'Based on your favorites' },
    { value: 'preference', label: 'Matched', icon: Settings, description: 'Based on your preferences' }
  ];

  useEffect(() => {
    if (user) {
      loadRecommendations();
    }
  }, [user, recommendationType]);

  const loadRecommendations = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const recs = await getRecommendations(user.uid, recommendationType, limit);
      setRecommendations(recs);
    } catch (error) {
      console.error('Error loading recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadRecommendations();
    setRefreshing(false);
  };

  const getCurrentTypeInfo = () => {
    return recommendationTypes.find(type => type.value === recommendationType) || recommendationTypes[0];
  };

  if (!user) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="text-center py-8">
          <Sparkles className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Get Personalized Recommendations</h3>
          <p className="text-gray-600 mb-4">
            Sign in to receive college recommendations tailored to your preferences and interests.
          </p>
          <Link href="/colleges" className="btn-primary">
            Explore All Colleges
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      {showHeader && (
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {React.createElement(getCurrentTypeInfo().icon, { 
                className: "w-5 h-5 text-primary-600" 
              })}
              <h2 className="text-xl font-semibold text-gray-900">
                {getCurrentTypeInfo().label} Recommendations
              </h2>
            </div>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="p-1 text-gray-400 hover:text-primary-600 transition-colors disabled:opacity-50"
              title="Refresh recommendations"
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            </button>
          </div>

          <div className="flex items-center space-x-2">
            {/* Recommendation Type Selector */}
            <select
              value={recommendationType}
              onChange={(e) => setRecommendationType(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {recommendationTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(limit)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      ) : recommendations.length === 0 ? (
        <div className="text-center py-8">
          <Sparkles className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No recommendations available</h3>
          <p className="text-gray-600 mb-4">
            {recommendationType === 'content' 
              ? 'Add some colleges to your favorites to get similar recommendations.'
              : 'Update your preferences to get better recommendations.'
            }
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Link href="/colleges" className="btn-secondary">
              Explore Colleges
            </Link>
            <Link href="/dashboard/profile" className="btn-primary">
              Update Preferences
            </Link>
          </div>
        </div>
      ) : (
        <>
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              {getCurrentTypeInfo().description} • {recommendations.length} colleges found
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recommendations.map((college) => (
              <div key={college.id} className="relative group">
                <div className="bg-gray-50 rounded-xl p-4 hover:shadow-md transition-shadow">
                  {/* College Image/Icon */}
                  <div className="w-full h-32 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg mb-4 flex items-center justify-center">
                    <div className="text-white text-center">
                      <Award className="w-8 h-8 mx-auto mb-2" />
                      <div className="text-sm font-semibold">Rank #{college.ranking}</div>
                    </div>
                  </div>

                  {/* College Info */}
                  <div className="mb-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                        {college.name}
                      </h3>
                      <FavoriteButton collegeId={college.id} size="sm" />
                    </div>
                    
                    <div className="flex items-center text-xs text-gray-500 mb-2">
                      <MapPin className="w-3 h-3 mr-1" />
                      <span>{college.location}</span>
                    </div>

                    <div className="flex items-center text-xs text-primary-600 mb-3">
                      <Star className="w-3 h-3 mr-1" />
                      <span>{college.reason}</span>
                    </div>
                  </div>

                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <div className="text-center bg-white rounded-lg p-2">
                      <div className="text-sm font-semibold text-green-600">
                        {college.placements?.percentage || 0}%
                      </div>
                      <div className="text-xs text-gray-500">Placement</div>
                    </div>
                    <div className="text-center bg-white rounded-lg p-2">
                      <div className="text-sm font-semibold text-blue-600">
                        ₹{((college.placements?.averagePackage || 0) / 100000).toFixed(1)}L
                      </div>
                      <div className="text-xs text-gray-500">Avg Package</div>
                    </div>
                  </div>

                  {/* Action Button */}
                  <Link
                    href={`/colleges/${college.id}`}
                    className="w-full bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors text-center block"
                  >
                    View Details
                  </Link>
                </div>

                {/* Recommendation Score Badge */}
                {college.score > 0.7 && (
                  <div className="absolute -top-2 -right-2 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-full">
                    {Math.round(college.score * 100)}% Match
                  </div>
                )}
              </div>
            ))}
          </div>

          {recommendations.length >= limit && (
            <div className="mt-6 text-center">
              <Link
                href="/colleges"
                className="inline-flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-medium"
              >
                <span>Explore more colleges</span>
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default RecommendationsSection;
