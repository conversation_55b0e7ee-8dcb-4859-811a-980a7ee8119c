import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  collection, 
  query, 
  where, 
  orderBy, 
  getDocs, 
  addDoc, 
  serverTimestamp,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';
import { db } from './firebase';

// User Profile Management
export const getUserProfile = async (userId) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userSnap = await getDoc(userRef);
    
    if (userSnap.exists()) {
      return { id: userSnap.id, ...userSnap.data() };
    }
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

export const updateUserProfile = async (userId, updates) => {
  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// Favorites Management
export const addToFavorites = async (userId, collegeId) => {
  try {
    const favoriteRef = doc(db, 'favorites', `${userId}_${collegeId}`);
    await setDoc(favoriteRef, {
      userId,
      collegeId,
      createdAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error adding to favorites:', error);
    throw error;
  }
};

export const removeFromFavorites = async (userId, collegeId) => {
  try {
    const favoriteRef = doc(db, 'favorites', `${userId}_${collegeId}`);
    await deleteDoc(favoriteRef);
  } catch (error) {
    console.error('Error removing from favorites:', error);
    throw error;
  }
};

export const getUserFavorites = async (userId) => {
  try {
    const favoritesQuery = query(
      collection(db, 'favorites'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(favoritesQuery);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting user favorites:', error);
    throw error;
  }
};

export const isFavorite = async (userId, collegeId) => {
  try {
    const favoriteRef = doc(db, 'favorites', `${userId}_${collegeId}`);
    const favoriteSnap = await getDoc(favoriteRef);
    return favoriteSnap.exists();
  } catch (error) {
    console.error('Error checking favorite status:', error);
    return false;
  }
};

// Application Tracking
export const addApplication = async (userId, applicationData) => {
  try {
    const applicationRef = await addDoc(collection(db, 'applications'), {
      userId,
      ...applicationData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    return applicationRef.id;
  } catch (error) {
    console.error('Error adding application:', error);
    throw error;
  }
};

export const updateApplication = async (applicationId, updates) => {
  try {
    const applicationRef = doc(db, 'applications', applicationId);
    await updateDoc(applicationRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating application:', error);
    throw error;
  }
};

export const deleteApplication = async (applicationId) => {
  try {
    const applicationRef = doc(db, 'applications', applicationId);
    await deleteDoc(applicationRef);
  } catch (error) {
    console.error('Error deleting application:', error);
    throw error;
  }
};

export const getUserApplications = async (userId) => {
  try {
    const applicationsQuery = query(
      collection(db, 'applications'),
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );
    
    const querySnapshot = await getDocs(applicationsQuery);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting user applications:', error);
    throw error;
  }
};

// User Preferences
export const updateUserPreferences = async (userId, preferences) => {
  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      preferences: {
        ...preferences
      },
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating user preferences:', error);
    throw error;
  }
};

// User Activity Tracking
export const trackUserActivity = async (userId, activity) => {
  try {
    await addDoc(collection(db, 'userActivity'), {
      userId,
      ...activity,
      timestamp: serverTimestamp()
    });
  } catch (error) {
    console.error('Error tracking user activity:', error);
    // Don't throw error for activity tracking to avoid disrupting user experience
  }
};

// Search History
export const addToSearchHistory = async (userId, searchQuery, filters = {}) => {
  try {
    await addDoc(collection(db, 'searchHistory'), {
      userId,
      query: searchQuery,
      filters,
      timestamp: serverTimestamp()
    });
  } catch (error) {
    console.error('Error adding to search history:', error);
  }
};

export const getUserSearchHistory = async (userId, limit = 10) => {
  try {
    const searchQuery = query(
      collection(db, 'searchHistory'),
      where('userId', '==', userId),
      orderBy('timestamp', 'desc')
    );
    
    const querySnapshot = await getDocs(searchQuery);
    return querySnapshot.docs.slice(0, limit).map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting search history:', error);
    return [];
  }
};

// Comparison History
export const addToComparisonHistory = async (userId, collegeIds, comparisonData = {}) => {
  try {
    await addDoc(collection(db, 'comparisonHistory'), {
      userId,
      collegeIds,
      ...comparisonData,
      timestamp: serverTimestamp()
    });
  } catch (error) {
    console.error('Error adding to comparison history:', error);
  }
};

export const getUserComparisonHistory = async (userId, limit = 10) => {
  try {
    const comparisonQuery = query(
      collection(db, 'comparisonHistory'),
      where('userId', '==', userId),
      orderBy('timestamp', 'desc')
    );
    
    const querySnapshot = await getDocs(comparisonQuery);
    return querySnapshot.docs.slice(0, limit).map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting comparison history:', error);
    return [];
  }
};

// User Statistics
export const getUserStats = async (userId) => {
  try {
    const [favorites, applications, searchHistory, comparisonHistory] = await Promise.all([
      getUserFavorites(userId),
      getUserApplications(userId),
      getUserSearchHistory(userId, 50),
      getUserComparisonHistory(userId, 50)
    ]);

    return {
      totalFavorites: favorites.length,
      totalApplications: applications.length,
      totalSearches: searchHistory.length,
      totalComparisons: comparisonHistory.length,
      recentActivity: {
        lastSearch: searchHistory[0]?.timestamp || null,
        lastComparison: comparisonHistory[0]?.timestamp || null,
        lastFavorite: favorites[0]?.createdAt || null
      }
    };
  } catch (error) {
    console.error('Error getting user stats:', error);
    return {
      totalFavorites: 0,
      totalApplications: 0,
      totalSearches: 0,
      totalComparisons: 0,
      recentActivity: {}
    };
  }
};
