'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Grid, List, Loader } from 'lucide-react';
import CollegeCard from '../../components/CollegeCard';
import SearchFilters from '../../components/SearchFilters';
import LazyCollegeGrid from '../../components/ui/LazyCollegeGrid';
import { getAllColleges, searchColleges, filterColleges, sortColleges } from '../../lib/collegeData';
import { preloadCollegeImages } from '../../lib/imagePreloader';

export default function CollegesPage() {
  const searchParams = useSearchParams();
  const [colleges, setColleges] = useState([]);
  const [filteredColleges, setFilteredColleges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid');

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({});
  const [sortBy, setSortBy] = useState('ranking');

  // Initialize search params safely
  useEffect(() => {
    if (searchParams) {
      setSearchQuery(searchParams.get('search') || '');
      setSortBy(searchParams.get('sort') || 'ranking');
    }
  }, [searchParams]);

  useEffect(() => {
    // Load all colleges
    const loadColleges = async () => {
      try {
        const allColleges = await getAllColleges();
        setColleges(allColleges);

        // Preload high-priority images (first 6 colleges)
        preloadCollegeImages(allColleges, 'high').then(() => {
          console.log('High-priority college images preloaded');
        });

        setLoading(false);
      } catch (error) {
        console.error('Failed to load colleges:', error);
        setLoading(false);
      }
    };
    loadColleges();
  }, []);

  useEffect(() => {
    // Apply search, filters, and sorting
    const applyFiltersAndSearch = async () => {
      let result = colleges;

      // Apply search
      if (searchQuery) {
        result = await searchColleges(searchQuery);
      }

      // Apply filters
      if (Object.keys(filters).length > 0) {
        result = await filterColleges(filters);
        if (searchQuery) {
          // If both search and filters are applied, intersect the results
          const searchResults = await searchColleges(searchQuery);
          result = result.filter(college =>
            searchResults.some(searchCollege => searchCollege.id === college.id)
          );
        }
      }

      // Apply sorting
      result = sortColleges(result, sortBy);

      setFilteredColleges(result);
    };

    if (colleges.length > 0) {
      applyFiltersAndSearch();
    }
  }, [colleges, searchQuery, filters, sortBy]);

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const handleFilter = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSort = (newSortBy) => {
    setSortBy(newSortBy);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader className="h-12 w-12 animate-spin text-primary-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading colleges...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="container-max py-8">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Engineering Colleges in Bangalore
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover and compare {colleges.length} top engineering colleges with detailed information 
              about placements, courses, and facilities.
            </p>
          </div>
        </div>
      </div>

      <div className="container-max py-8">
        {/* Search and Filters */}
        <SearchFilters
          onSearch={handleSearch}
          onFilter={handleFilter}
          onSort={handleSort}
          searchQuery={searchQuery}
          filters={filters}
          sortBy={sortBy}
        />

        {/* Results Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {filteredColleges.length} College{filteredColleges.length !== 1 ? 's' : ''} Found
            </h2>
            {searchQuery && (
              <p className="text-gray-600 mt-1">
                Results for "{searchQuery}"
              </p>
            )}
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'grid'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              aria-label="Grid view"
            >
              <Grid className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'list'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              aria-label="List view"
            >
              <List className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Results */}
        {filteredColleges.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
              <Grid className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No colleges found</h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your search criteria or filters to find more results.
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setFilters({});
                setSortBy('ranking');
              }}
              className="btn-primary"
            >
              Clear All Filters
            </button>
          </div>
        ) : viewMode === 'grid' ? (
          <LazyCollegeGrid colleges={filteredColleges} itemsPerPage={12} />
        ) : (
          <div className="space-y-6">
            {filteredColleges.map((college) => (
              <CollegeCard
                key={college.id}
                college={college}
                isCompact={true}
                showCompareButton={true}
              />
            ))}
          </div>
        )}

        {/* Load More Button (if needed for pagination) */}
        {filteredColleges.length > 0 && filteredColleges.length >= 12 && (
          <div className="text-center mt-12">
            <button className="btn-secondary">
              Load More Colleges
            </button>
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className="bg-primary-600 text-white py-12">
        <div className="container-max">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">{filteredColleges.length}</div>
              <div className="text-primary-100">Colleges Available</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">
                {Math.round(
                  filteredColleges.reduce((sum, college) => sum + college.placementRate, 0) / 
                  Math.max(filteredColleges.length, 1)
                )}%
              </div>
              <div className="text-primary-100">Average Placement Rate</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">
                ₹{Math.max(...filteredColleges.map(c => c.highestPackage), 0)} LPA
              </div>
              <div className="text-primary-100">Highest Package</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
